/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('@arco-design/web-vue')['Alert']
    ABadge: typeof import('@arco-design/web-vue')['Badge']
    AButton: typeof import('@arco-design/web-vue')['Button']
    AButtonGroup: typeof import('@arco-design/web-vue')['ButtonGroup']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACol: typeof import('@arco-design/web-vue')['Col']
    AConfigProvider: typeof import('@arco-design/web-vue')['ConfigProvider']
    ADescriptions: typeof import('@arco-design/web-vue')['Descriptions']
    ADescriptionsItem: typeof import('@arco-design/web-vue')['DescriptionsItem']
    AEmpty: typeof import('@arco-design/web-vue')['Empty']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputGroup: typeof import('@arco-design/web-vue')['InputGroup']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputSearch: typeof import('@arco-design/web-vue')['InputSearch']
    ALayout: typeof import('@arco-design/web-vue')['Layout']
    ALayoutContent: typeof import('@arco-design/web-vue')['LayoutContent']
    ALayoutHeader: typeof import('@arco-design/web-vue')['LayoutHeader']
    ALayoutSider: typeof import('@arco-design/web-vue')['LayoutSider']
    AList: typeof import('@arco-design/web-vue')['List']
    AListItem: typeof import('@arco-design/web-vue')['ListItem']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APopconfirm: typeof import('@arco-design/web-vue')['Popconfirm']
    AProgress: typeof import('@arco-design/web-vue')['Progress']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARow: typeof import('@arco-design/web-vue')['Row']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASlider: typeof import('@arco-design/web-vue')['Slider']
    AStatistic: typeof import('@arco-design/web-vue')['Statistic']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATree: typeof import('@arco-design/web-vue')['Tree']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
