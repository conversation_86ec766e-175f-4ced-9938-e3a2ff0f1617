<template>
  <div class="operation-page">
    <a-row :gutter="20">
      <!-- 左侧：截图和DOM树 -->
      <a-col :span="10">
        <a-card title="设备屏幕和DOM树" class="screen-card">
          <!-- 操作按钮 -->
          <div class="operation-buttons">
            <a-button
              type="primary"
              @click="takeScreenshot(false)"
              :loading="screenshotLoading"
              :disabled="!deviceStore.isConnected"
            >
              <template #icon><icon-camera /></template>
              截图
            </a-button>
            <a-button
              @click="takeScreenshot(true)"
              :loading="screenshotLoading"
              :disabled="!deviceStore.isConnected"
            >
              <template #icon><icon-camera /></template>
              截图并标记元素
            </a-button>
            <a-button
              @click="getDomTree"
              :loading="domLoading"
              :disabled="!deviceStore.isConnected"
            >
              <template #icon><icon-code /></template>
              获取DOM树
            </a-button>
            <a-button
              @click="openScreenshotManager"
            >
              <template #icon><icon-folder /></template>
              截图管理
            </a-button>
          </div>

          <!-- 截图显示区域 -->
          <div v-if="currentScreenshot" class="screenshot-section">
            <div class="screenshot-header">
              <h4>设备截图</h4>
              <div class="screenshot-actions">
                <a-button size="small" @click="zoomIn">
                  <template #icon><icon-zoom-in /></template>
                </a-button>
                <a-button size="small" @click="zoomOut">
                  <template #icon><icon-zoom-out /></template>
                </a-button>
                <a-button size="small" @click="resetZoom">
                  <template #icon><icon-fullscreen /></template>
                </a-button>
              </div>
            </div>
            <div class="screenshot-container" ref="screenshotContainer">
              <img 
                :src="screenshotUrl" 
                :style="{ transform: `scale(${zoomLevel})` }"
                @click="onScreenshotClick"
                class="screenshot-image"
              />
            </div>
          </div>

          <!-- DOM树显示区域 -->
          <div v-if="domTree" class="dom-tree-section">
            <div class="dom-tree-header">
              <h4>DOM树结构</h4>
              <div class="dom-tree-actions">
                <a-input-search
                  v-model="searchKeyword"
                  placeholder="搜索元素（支持seq_index、text、class等）"
                  @search="searchDomTree"
                  style="width: 300px"
                />
              </div>
            </div>
            <div class="dom-tree-container">
              <a-tree
                :data="filteredDomTree"
                :show-line="true"
                :default-expand-all="false"
                @select="onDomNodeSelect"
              >
                <template #title="{ title, seq_index, text, visible }">
                  <div class="dom-node-title">
                    <span class="seq-index">{{ seq_index }}</span>
                    <span class="node-name">{{ title }}</span>
                    <span v-if="text" class="node-text">{{ text }}</span>
                    <a-tag v-if="!visible" color="red" size="small">隐藏</a-tag>
                  </div>
                </template>
              </a-tree>
            </div>
          </div>

        </a-card>
      </a-col>

      <!-- 中间：操作面板 -->
      <a-col :span="8">
        <a-card class="operation-card">
          <template #title>
            <div class="operation-card-title">
              <span>操作面板</span>
              <!-- 自动截图设置 -->
              <div class="auto-screenshot-setting">
                <a-tooltip content="启用后，每次操作成功后会自动截图并标记元素">
                  <a-switch
                    v-model="autoScreenshotEnabled"
                    size="small"
                  >
                    <template #checked>
                      <icon-camera />
                    </template>
                    <template #unchecked>
                      <icon-camera />
                    </template>
                  </a-switch>
                </a-tooltip>
                <span class="setting-label">操作后自动截图</span>
              </div>
            </div>
          </template>

          <a-tabs v-model:active-key="activeTab" type="card">
            <!-- 点击操作 -->
            <a-tab-pane key="click" title="点击操作">
              <a-form :model="clickForm" layout="vertical">
                <a-form-item label="点击方式">
                  <a-radio-group v-model="clickForm.method">
                    <a-radio value="coordinates">坐标点击</a-radio>
                    <a-radio value="seq_index">索引点击</a-radio>
                    <a-radio value="poco_query">Poco查询</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <template v-if="clickForm.method === 'coordinates'">
                  <a-row :gutter="10">
                    <a-col :span="12">
                      <a-form-item label="X坐标">
                        <a-input-number v-model="clickForm.x" :min="0" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="Y坐标">
                        <a-input-number v-model="clickForm.y" :min="0" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </template>
                
                <a-form-item v-if="clickForm.method === 'seq_index'" label="元素索引">
                  <a-input v-model="clickForm.seq_index" placeholder="如: 1.2.3" />
                </a-form-item>
                
                <a-form-item v-if="clickForm.method === 'poco_query'" label="Poco查询">
                  <a-textarea 
                    v-model="clickForm.poco_query" 
                    placeholder="如: ('com.example:id/button').click()"
                    :rows="3"
                  />
                </a-form-item>
                
                <a-form-item>
                  <a-button 
                    type="primary" 
                    @click="performClick"
                    :loading="clickLoading"
                    :disabled="!deviceStore.isConnected"
                    block
                  >
                    执行点击
                  </a-button>
                </a-form-item>
              </a-form>
            </a-tab-pane>

            <!-- 输入操作 -->
            <a-tab-pane key="input" title="输入操作">
              <a-form :model="inputForm" layout="vertical">
                <a-form-item label="输入方式">
                  <a-radio-group v-model="inputForm.method">
                    <a-radio value="coordinates">坐标输入</a-radio>
                    <a-radio value="seq_index">索引输入</a-radio>
                    <a-radio value="poco_query">Poco查询</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <template v-if="inputForm.method === 'coordinates'">
                  <a-row :gutter="10">
                    <a-col :span="12">
                      <a-form-item label="X坐标">
                        <a-input-number v-model="inputForm.x" :min="0" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="12">
                      <a-form-item label="Y坐标">
                        <a-input-number v-model="inputForm.y" :min="0" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </template>
                
                <a-form-item v-if="inputForm.method === 'seq_index'" label="元素索引">
                  <a-input v-model="inputForm.seq_index" placeholder="如: 1.2.3" />
                </a-form-item>
                
                <a-form-item v-if="inputForm.method === 'poco_query'" label="Poco查询">
                  <a-textarea 
                    v-model="inputForm.poco_query" 
                    placeholder="如: ('com.example:id/edittext')"
                    :rows="2"
                  />
                </a-form-item>
                
                <a-form-item label="输入内容" required>
                  <a-textarea v-model="inputForm.text" placeholder="请输入要输入的文本" />
                </a-form-item>
                
                <a-form-item>
                  <a-button 
                    type="primary" 
                    @click="performInput"
                    :loading="inputLoading"
                    :disabled="!deviceStore.isConnected || !inputForm.text"
                    block
                  >
                    执行输入
                  </a-button>
                </a-form-item>
              </a-form>
            </a-tab-pane>

            <!-- 滑动操作 -->
            <a-tab-pane key="swipe" title="滑动操作">
              <a-form :model="swipeForm" layout="vertical">
                <a-form-item label="滑动方式">
                  <a-radio-group v-model="swipeForm.method">
                    <a-radio value="default">屏幕滑动</a-radio>
                    <a-radio value="poco_query">元素滑动</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item label="滑动方向">
                  <a-select v-model="swipeForm.direction">
                    <a-option value="up">向上</a-option>
                    <a-option value="down">向下</a-option>
                    <a-option value="left">向左</a-option>
                    <a-option value="right">向右</a-option>
                  </a-select>
                </a-form-item>
                
                <a-form-item v-if="swipeForm.method === 'default'" label="滑动距离">
                  <a-slider 
                    v-model="swipeForm.distance" 
                    :min="0.1" 
                    :max="1" 
                    :step="0.1"
                    :format-tooltip="(value) => `${value * 100}%`"
                  />
                </a-form-item>
                
                <a-form-item v-if="swipeForm.method === 'poco_query'" label="Poco查询">
                  <a-textarea 
                    v-model="swipeForm.poco_query" 
                    placeholder="如: ('com.example:id/scrollview')"
                    :rows="2"
                  />
                </a-form-item>
                
                <a-form-item>
                  <a-button 
                    type="primary" 
                    @click="performSwipe"
                    :loading="swipeLoading"
                    :disabled="!deviceStore.isConnected"
                    block
                  >
                    执行滑动
                  </a-button>
                </a-form-item>
              </a-form>
            </a-tab-pane>

            <!-- 断言操作 -->
            <a-tab-pane key="assert" title="断言操作">
              <a-form :model="assertForm" layout="vertical">
                <a-form-item label="Poco查询" required>
                  <a-textarea 
                    v-model="assertForm.poco_query" 
                    placeholder="如: ('com.example:id/button')"
                    :rows="3"
                  />
                </a-form-item>
                
                <a-form-item>
                  <a-button 
                    type="primary" 
                    @click="performAssert"
                    :loading="assertLoading"
                    :disabled="!deviceStore.isConnected || !assertForm.poco_query"
                    block
                  >
                    执行断言
                  </a-button>
                </a-form-item>
                
                <div v-if="assertResult !== null" class="assert-result">
                  <a-alert 
                    :type="assertResult ? 'success' : 'error'"
                    :message="assertResult ? '元素存在' : '元素不存在'"
                    show-icon
                  />
                </div>
              </a-form>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-col>

      <!-- 右侧：元素详情面板 -->
      <a-col :span="6">
        <a-card title="元素详情" class="element-details-card">
          <div v-if="selectedElement" class="element-details-content">
            <a-descriptions :column="1" size="small" bordered>
              <a-descriptions-item label="序号">
                <a-tag color="blue">{{ selectedElement.seq_index }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="层级索引">
                <a-tag color="green">{{ selectedElement.index }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="元素名称">
                {{ selectedElement.name || '无' }}
              </a-descriptions-item>
              <a-descriptions-item label="文本内容">
                {{ selectedElement.text || '无' }}
              </a-descriptions-item>
              <a-descriptions-item label="位置">
                {{ selectedElement.pos ? `(${selectedElement.pos[0]}, ${selectedElement.pos[1]})` : '无' }}
              </a-descriptions-item>
              <a-descriptions-item label="大小">
                {{ selectedElement.size ? `${selectedElement.size[0]} × ${selectedElement.size[1]}` : '无' }}
              </a-descriptions-item>
              <a-descriptions-item label="可见性">
                <a-tag :color="selectedElement.visible ? 'green' : 'red'">
                  {{ selectedElement.visible ? '可见' : '隐藏' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="可用性">
                <a-tag :color="selectedElement.enabled ? 'green' : 'red'">
                  {{ selectedElement.enabled ? '可用' : '禁用' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="类名">
                {{ selectedElement.attributes?.class || '无' }}
              </a-descriptions-item>
              <a-descriptions-item label="资源ID">
                {{ selectedElement.attributes?.resource_id || '无' }}
              </a-descriptions-item>
              <a-descriptions-item label="包名">
                {{ selectedElement.attributes?.package || '无' }}
              </a-descriptions-item>
              <a-descriptions-item label="边界">
                {{ selectedElement.attributes?.bounds ?
                    `[${selectedElement.attributes.bounds.join(', ')}]` : '无' }}
              </a-descriptions-item>
            </a-descriptions>

            <!-- 快速操作按钮 -->
            <div class="element-actions">
              <a-button
                size="small"
                type="primary"
                @click="fillClickForm"
                block
                style="margin-bottom: 8px"
              >
                填充到点击操作
              </a-button>
              <a-button
                size="small"
                type="outline"
                @click="fillInputForm"
                block
              >
                填充到输入操作
              </a-button>
            </div>
          </div>
          <div v-else class="no-element-selected">
            <a-empty description="请选择一个DOM元素" />
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 截图管理模态框 -->
    <a-modal
      v-model:visible="showScreenshotManager"
      title="截图管理"
      width="800px"
      :footer="false"
    >
      <div class="screenshot-manager">
        <div class="manager-header">
          <a-button
            type="primary"
            @click="refreshScreenshots"
            :loading="loadingScreenshots"
          >
            <template #icon><icon-refresh /></template>
            刷新
          </a-button>
          <a-popconfirm
            content="确定要删除所有截图吗？此操作不可恢复！"
            @ok="deleteAllScreenshots"
          >
            <a-button status="danger" type="outline">
              <template #icon><icon-delete /></template>
              删除所有
            </a-button>
          </a-popconfirm>
        </div>

        <div class="screenshots-grid">
          <div
            v-for="screenshot in screenshots"
            :key="screenshot.filename"
            class="screenshot-item"
          >
            <div class="screenshot-preview">
              <img
                :src="`/api/android/operation/screenshot/${screenshot.filename}`"
                :alt="screenshot.filename"
                @click="viewScreenshot(screenshot)"
              />
            </div>
            <div class="screenshot-info">
              <div class="filename">{{ screenshot.filename }}</div>
              <div class="time">{{ formatTime(screenshot.created_time) }}</div>
              <div class="size">{{ formatFileSize(screenshot.size) }}</div>
            </div>
            <div class="screenshot-actions">
              <a-button
                size="small"
                @click="viewScreenshot(screenshot)"
              >
                查看
              </a-button>
              <a-popconfirm
                content="确定要删除这个截图吗？"
                @ok="deleteScreenshot(screenshot.filename)"
              >
                <a-button size="small" status="danger" type="outline">
                  删除
                </a-button>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <a-empty v-if="screenshots.length === 0 && !loadingScreenshots" description="暂无截图" />
      </div>
    </a-modal>

    <!-- 截图查看模态框 -->
    <a-modal
      v-model:visible="showScreenshotViewer"
      :title="currentViewingScreenshot?.filename"
      width="90%"
      :footer="false"
    >
      <div class="screenshot-viewer" v-if="currentViewingScreenshot">
        <img
          :src="`/api/android/operation/screenshot/${currentViewingScreenshot.filename}`"
          :alt="currentViewingScreenshot.filename"
          style="max-width: 100%; height: auto;"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { operationApi } from '@/api/operation'
import { Message } from '@arco-design/web-vue'

const deviceStore = useDeviceStore()

// 响应式数据
const activeTab = ref('click')
const currentScreenshot = ref('')
const domTree = ref(null)
const searchKeyword = ref('')
const zoomLevel = ref(1)
const selectedElement = ref(null)

// 截图管理相关
const showScreenshotManager = ref(false)
const showScreenshotViewer = ref(false)
const screenshots = ref([])
const currentViewingScreenshot = ref(null)
const loadingScreenshots = ref(false)

// 自动截图设置
const autoScreenshotEnabled = ref(false)

// 加载状态
const screenshotLoading = ref(false)
const domLoading = ref(false)
const clickLoading = ref(false)
const inputLoading = ref(false)
const swipeLoading = ref(false)
const assertLoading = ref(false)

// 表单数据
const clickForm = reactive({
  method: 'coordinates',
  x: 0,
  y: 0,
  seq_index: '',
  poco_query: ''
})

const inputForm = reactive({
  method: 'coordinates',
  x: 0,
  y: 0,
  seq_index: '',
  poco_query: '',
  text: ''
})

const swipeForm = reactive({
  method: 'default',
  direction: 'up',
  distance: 0.3,
  poco_query: ''
})

const assertForm = reactive({
  poco_query: ''
})

const assertResult = ref(null)

// 计算属性
const screenshotUrl = computed(() => {
  if (currentScreenshot.value) {
    return `/api/android/operation/screenshot/${currentScreenshot.value.split('/').pop()}`
  }
  return ''
})

const filteredDomTree = computed(() => {
  if (!domTree.value) {
    return []
  }

  if (!searchKeyword.value) {
    return convertDomTreeToTreeData(domTree.value.elements)
  }

  // 搜索过滤逻辑
  const keyword = searchKeyword.value.toLowerCase()
  const filteredElements = domTree.value.elements.filter(element => {
    return String(element.seq_index).includes(keyword) ||
           element.index.toLowerCase().includes(keyword) ||
           (element.name && element.name.toLowerCase().includes(keyword)) ||
           (element.text && element.text.toLowerCase().includes(keyword)) ||
           (element.attributes && element.attributes.class && element.attributes.class.toLowerCase().includes(keyword)) ||
           (element.attributes && element.attributes.resource_id && element.attributes.resource_id.toLowerCase().includes(keyword))
  })

  return convertDomTreeToTreeData(filteredElements)
})

// 方法
const takeScreenshot = async (markElements = false) => {
  screenshotLoading.value = true
  try {
    const response = await operationApi.takeScreenshot({ mark_elements: markElements })
    currentScreenshot.value = response.data.file_path
    Message.success('截图成功')

    // 如果是标记元素的截图，同时获取DOM树
    if (markElements) {
      await getDomTree()
    }
  } catch (error) {
    Message.error('截图失败: ' + error.message)
  } finally {
    screenshotLoading.value = false
  }
}

const getDomTree = async () => {
  domLoading.value = true
  try {
    const response = await operationApi.getDomTree()
    domTree.value = response.data
    Message.success(`获取DOM树成功，共 ${response.data.total_elements} 个元素`)
  } catch (error) {
    Message.error('获取DOM树失败: ' + error.message)
  } finally {
    domLoading.value = false
  }
}

const performClick = async () => {
  clickLoading.value = true
  try {
    const response = await operationApi.clickElement(clickForm)
    if (response.data.success) {
      Message.success('点击操作成功')

      // 如果启用了自动截图，执行截图并标记元素
      if (autoScreenshotEnabled.value) {
        await takeScreenshot(true)
      }
    } else {
      Message.error('点击操作失败')
    }
  } catch (error) {
    Message.error('点击操作失败: ' + error.message)
  } finally {
    clickLoading.value = false
  }
}

const performInput = async () => {
  inputLoading.value = true
  try {
    const response = await operationApi.inputText(inputForm)
    if (response.data.success) {
      Message.success('输入操作成功')

      // 如果启用了自动截图，执行截图并标记元素
      if (autoScreenshotEnabled.value) {
        await takeScreenshot(true)
      }
    } else {
      Message.error('输入操作失败')
    }
  } catch (error) {
    Message.error('输入操作失败: ' + error.message)
  } finally {
    inputLoading.value = false
  }
}

const performSwipe = async () => {
  swipeLoading.value = true
  try {
    const response = await operationApi.swipeScreen(swipeForm)
    if (response.data.success) {
      Message.success('滑动操作成功')

      // 如果启用了自动截图，执行截图并标记元素
      if (autoScreenshotEnabled.value) {
        await takeScreenshot(true)
      }
    } else {
      Message.error('滑动操作失败')
    }
  } catch (error) {
    Message.error('滑动操作失败: ' + error.message)
  } finally {
    swipeLoading.value = false
  }
}

const performAssert = async () => {
  assertLoading.value = true
  try {
    const response = await operationApi.assertElement(assertForm)
    assertResult.value = response.data.exists
    Message.success(response.data.message)
  } catch (error) {
    Message.error('断言操作失败: ' + error.message)
    assertResult.value = false
  } finally {
    assertLoading.value = false
  }
}

// 工具方法
const convertDomTreeToTreeData = (elements) => {
  // 将扁平的元素列表转换为树形结构
  const treeData = []
  const elementMap = new Map()

  elements.forEach(element => {
    const treeNode = {
      key: element.seq_index,
      title: element.name || 'Unknown',
      seq_index: element.seq_index,
      index: element.index,
      text: element.text,
      visible: element.visible,
      children: [],
      ...element
    }
    elementMap.set(element.index, treeNode)
  })

  // 构建树形结构 - 使用index而不是seq_index来构建层级关系
  elements.forEach(element => {
    const node = elementMap.get(element.index)
    const parentIndex = getParentIndex(element.index)

    if (parentIndex && elementMap.has(parentIndex)) {
      elementMap.get(parentIndex).children.push(node)
    } else {
      treeData.push(node)
    }
  })

  return treeData
}

const getParentIndex = (index) => {
  // index格式如: "0", "0_0", "0_0_1"
  const parts = index.split('_')
  if (parts.length > 1) {
    return parts.slice(0, -1).join('_')
  }
  return null
}

const onScreenshotClick = (event) => {
  // 获取点击坐标
  const rect = event.target.getBoundingClientRect()
  const img = event.target

  // 计算实际图片尺寸与显示尺寸的比例
  const scaleX = img.naturalWidth / img.clientWidth
  const scaleY = img.naturalHeight / img.clientHeight

  // 计算在原始图片上的坐标
  const x = Math.round((event.clientX - rect.left) * scaleX)
  const y = Math.round((event.clientY - rect.top) * scaleY)

  // 自动填充到点击表单
  clickForm.x = x
  clickForm.y = y
  clickForm.method = 'coordinates'

  // 尝试查找点击位置对应的DOM元素
  const clickedElement = findElementByCoordinates(x, y)
  if (clickedElement) {
    selectedElement.value = clickedElement
    Message.success(`已选中元素: ${clickedElement.seq_index} (${clickedElement.name || 'Unknown'})`)

    // 自动填充到相关表单
    clickForm.seq_index = clickedElement.seq_index
    inputForm.seq_index = clickedElement.seq_index
  } else {
    Message.info(`已获取点击坐标: (${x}, ${y})`)
  }
}

const onDomNodeSelect = (_, { node }) => {
  selectedElement.value = node
  
  // 自动填充到相关表单
  if (node.seq_index) {
    clickForm.seq_index = node.seq_index
    inputForm.seq_index = node.seq_index
  }
}

const searchDomTree = () => {
  // 搜索逻辑已在计算属性中实现
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.2, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.2, 0.2)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 截图管理方法
const openScreenshotManager = async () => {
  showScreenshotManager.value = true
  await refreshScreenshots()
}

const refreshScreenshots = async () => {
  loadingScreenshots.value = true
  try {
    const response = await operationApi.getScreenshots()
    screenshots.value = response.data.screenshots || []
  } catch (error) {
    Message.error('获取截图列表失败: ' + error.message)
  } finally {
    loadingScreenshots.value = false
  }
}

const deleteScreenshot = async (filename) => {
  try {
    await operationApi.deleteScreenshot(filename)
    Message.success('截图删除成功')
    await refreshScreenshots()
  } catch (error) {
    Message.error('删除截图失败: ' + error.message)
  }
}

const deleteAllScreenshots = async () => {
  try {
    await operationApi.deleteAllScreenshots()
    Message.success('所有截图删除成功')
    await refreshScreenshots()
  } catch (error) {
    Message.error('删除所有截图失败: ' + error.message)
  }
}

const viewScreenshot = (screenshot) => {
  currentViewingScreenshot.value = screenshot
  showScreenshotViewer.value = true
}

const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString()
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const fillClickForm = () => {
  if (selectedElement.value) {
    clickForm.method = 'seq_index'
    clickForm.seq_index = selectedElement.value.seq_index
    activeTab.value = 'click'
    Message.success('已填充到点击操作')
  }
}

const fillInputForm = () => {
  if (selectedElement.value) {
    inputForm.method = 'seq_index'
    inputForm.seq_index = selectedElement.value.seq_index
    activeTab.value = 'input'
    Message.success('已填充到输入操作')
  }
}

const findElementByCoordinates = (x, y) => {
  if (!domTree.value || !domTree.value.elements) {
    return null
  }

  // 查找包含点击坐标的元素
  let bestMatch = null
  let smallestArea = Infinity

  for (const element of domTree.value.elements) {
    const bounds = element.attributes?.bounds
    if (bounds && bounds.length === 4) {
      const [left, top, right, bottom] = bounds

      // 检查点击坐标是否在元素边界内
      if (x >= left && x <= right && y >= top && y <= bottom) {
        const area = (right - left) * (bottom - top)

        // 选择面积最小的元素（最精确的匹配）
        if (area < smallestArea) {
          smallestArea = area
          bestMatch = element
        }
      }
    }
  }

  return bestMatch
}
</script>

<style scoped>
.operation-page {
  min-height: 100vh;
  padding: 20px;
}

.screen-card,
.operation-card,
.element-details-card {
  height: auto;
  min-height: 500px;
}

.operation-card :deep(.arco-card-body) {
  padding: 20px;
}

.operation-card :deep(.arco-tabs-content) {
  padding-top: 16px;
}

.operation-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.operation-card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.auto-screenshot-setting {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-label {
  font-size: 12px;
  color: var(--color-text-2);
}

.screenshot-section,
.dom-tree-section {
  margin-bottom: 20px;
}

.screenshot-header,
.dom-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.screenshot-header h4,
.dom-tree-header h4 {
  margin: 0;
}

.screenshot-actions {
  display: flex;
  gap: 8px;
}

.screenshot-container {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: auto;
  text-align: center;
  background: #f5f5f5;
}

.screenshot-image {
  max-width: 100%;
  cursor: crosshair;
  transition: transform 0.2s;
}

.dom-tree-container {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 12px;
  overflow: auto;
}

.dom-node-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.seq-index {
  background: var(--color-primary-light-1);
  color: var(--color-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
}

.node-name {
  font-weight: 500;
}

.node-text {
  color: var(--color-text-3);
  font-size: 12px;
}

.assert-result {
  margin-top: 12px;
}

/* 截图管理样式 */
.screenshot-manager {
  overflow-y: auto;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border);
}

.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.screenshot-item {
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.screenshot-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.screenshot-preview {
  height: 120px;
  overflow: hidden;
  background: var(--color-fill-1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.screenshot-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: pointer;
}

.screenshot-info {
  padding: 8px 12px;
  background: var(--color-bg-2);
}

.screenshot-info .filename {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.screenshot-info .time {
  font-size: 11px;
  color: var(--color-text-3);
  margin-bottom: 2px;
}

.screenshot-info .size {
  font-size: 11px;
  color: var(--color-text-3);
}

.screenshot-actions {
  padding: 8px 12px;
  display: flex;
  gap: 8px;
  border-top: 1px solid var(--color-border);
}

.screenshot-viewer {
  text-align: center;
  max-height: 80vh;
  overflow: auto;
}

/* 元素详情面板样式 */
.element-details-card {
  position: sticky;
  top: 20px;
}

.element-details-content {
  padding: 0;
}

.element-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
}

.no-element-selected {
  padding: 40px 0;
  text-align: center;
}
</style>
